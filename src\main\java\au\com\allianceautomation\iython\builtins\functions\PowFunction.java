package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python pow() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PowFunction extends AbstractBuiltinFunction {
    
    public PowFunction() {
        super("pow", 2, 3, "pow(x, y[, z]) -> number");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object base = args.get(0);
        Object exponent = args.get(1);
        Object modulus = args.size() > 2 ? args.get(2) : null;
        
        if (!(base instanceof Number) || !(exponent instanceof Number)) {
            throw new RuntimeException("pow() arguments must be numeric");
        }
        
        if (modulus != null && !(modulus instanceof Number)) {
            throw new RuntimeException("pow() modulus must be numeric");
        }
        
        double baseVal = ((Number) base).doubleValue();
        double expVal = ((Number) exponent).doubleValue();
        
        if (modulus != null) {
            // Three-argument form: pow(x, y, z) = (x**y) % z
            double modVal = ((Number) modulus).doubleValue();
            if (modVal == 0) {
                throw new RuntimeException("pow() 3rd argument cannot be 0");
            }
            
            // For integer arguments, use modular exponentiation
            if (base instanceof Integer && exponent instanceof Integer && modulus instanceof Integer) {
                long result = modPow(((Integer) base).longValue(), 
                                   ((Integer) exponent).longValue(), 
                                   ((Integer) modulus).longValue());
                return (int) result;
            } else {
                double result = Math.pow(baseVal, expVal) % modVal;
                return result;
            }
        } else {
            // Two-argument form: pow(x, y) = x**y
            double result = Math.pow(baseVal, expVal);
            
            // Return integer if both arguments are integers and result is whole
            if (base instanceof Integer && exponent instanceof Integer && 
                result == Math.floor(result) && !Double.isInfinite(result)) {
                return (int) result;
            }
            
            return result;
        }
    }
    
    /**
     * Modular exponentiation for integer arguments.
     */
    private long modPow(long base, long exp, long mod) {
        if (mod == 1) return 0;
        
        long result = 1;
        base = base % mod;
        
        while (exp > 0) {
            if (exp % 2 == 1) {
                result = (result * base) % mod;
            }
            exp = exp >> 1;
            base = (base * base) % mod;
        }
        
        return result;
    }
}
