package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * Python round() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class RoundFunction extends AbstractBuiltinFunction {
    
    public RoundFunction() {
        super("round", 1, 2, "round(number[, ndigits]) -> number");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object number = args.get(0);
        int ndigits = 0;
        
        if (args.size() > 1) {
            Object ndigitsObj = args.get(1);
            if (ndigitsObj instanceof Number) {
                ndigits = ((Number) ndigitsObj).intValue();
            } else {
                throw new RuntimeException("'" + ndigitsObj.getClass().getSimpleName() + "' object cannot be interpreted as an integer");
            }
        }
        
        if (!(number instanceof Number)) {
            throw new RuntimeException("must be real number, not " + number.getClass().getSimpleName());
        }
        
        double value = ((Number) number).doubleValue();
        
        if (ndigits == 0) {
            // Round to nearest integer
            return (int) Math.round(value);
        } else {
            // Round to specified decimal places
            BigDecimal bd = BigDecimal.valueOf(value);
            bd = bd.setScale(ndigits, RoundingMode.HALF_UP);
            return bd.doubleValue();
        }
    }
}
