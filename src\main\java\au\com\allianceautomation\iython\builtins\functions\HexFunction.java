package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python hex() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class HexFunction extends AbstractBuiltinFunction {
    
    public HexFunction() {
        super("hex", 1, 1, "hex(number) -> string");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (!(obj instanceof Number)) {
            throw new RuntimeException("'" + obj.getClass().getSimpleName() + "' object cannot be interpreted as an integer");
        }
        
        long value = ((Number) obj).longValue();
        
        if (value >= 0) {
            return "0x" + Long.toHexString(value);
        } else {
            return "-0x" + Long.toHexString(-value);
        }
    }
}
