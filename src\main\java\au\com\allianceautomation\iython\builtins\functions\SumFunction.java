package au.com.allianceautomation.iython.builtins.functions;

import java.util.List;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;

/**
 * Python sum() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class SumFunction extends AbstractBuiltinFunction {
    
    public SumFunction() {
        super("sum", 1, 2, "sum(iterable[, start]) -> value");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        // TODO: Implement sum function
        return 0;
    }
}


