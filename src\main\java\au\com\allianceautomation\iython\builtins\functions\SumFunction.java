package au.com.allianceautomation.iython.builtins.functions;

import java.util.List;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;

/**
 * Python sum() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class SumFunction extends AbstractBuiltinFunction {
    
    public SumFunction() {
        super("sum", 1, 2, "sum(iterable[, start]) -> value");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object iterable = args.get(0);
        Object start = args.size() > 1 ? args.get(1) : 0;

        if (!(iterable instanceof List)) {
            throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
        }

        List<?> list = (List<?>) iterable;

        // Start with the start value
        Number result;
        if (start instanceof Number) {
            result = (Number) start;
        } else {
            throw new RuntimeException("sum() can't sum strings [use ''.join(seq) instead]");
        }

        // Sum all elements
        for (Object item : list) {
            if (!(item instanceof Number)) {
                throw new RuntimeException("unsupported operand type(s) for +: '" +
                                         result.getClass().getSimpleName() + "' and '" +
                                         item.getClass().getSimpleName() + "'");
            }

            Number num = (Number) item;

            // Handle different number types
            if (result instanceof Double || num instanceof Double) {
                result = result.doubleValue() + num.doubleValue();
            } else if (result instanceof Float || num instanceof Float) {
                result = result.floatValue() + num.floatValue();
            } else if (result instanceof Long || num instanceof Long) {
                result = result.longValue() + num.longValue();
            } else {
                result = result.intValue() + num.intValue();
            }
        }

        return result;
    }
}


