package au.com.allianceautomation.iython.builtins;

import java.util.List;

/**
 * Interface for Python builtin functions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public interface BuiltinFunction {
    
    /**
     * Get the name of this builtin function.
     * 
     * @return The function name
     */
    String getName();
    
    /**
     * Call this builtin function with the given arguments.
     * 
     * @param args The arguments to pass to the function
     * @return The result of the function call
     * @throws RuntimeException if the function call fails
     */
    Object call(List<Object> args);
    
    /**
     * Get the minimum number of arguments this function accepts.
     * 
     * @return The minimum argument count
     */
    int getMinArgs();
    
    /**
     * Get the maximum number of arguments this function accepts.
     * Returns -1 for unlimited arguments.
     * 
     * @return The maximum argument count or -1 for unlimited
     */
    int getMaxArgs();
    
    /**
     * Get a description of this function for help/documentation.
     * 
     * @return A description of the function
     */
    String getDescription();
}
