package au.com.allianceautomation.iython.builtins;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import au.com.allianceautomation.iython.PythonExecutor;
import au.com.allianceautomation.iython.PythonExecutionException;

/**
 * Unit tests for Python builtin functions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class BuiltinFunctionsTest {
    
    @Test
    @DisplayName("Test builtin registry has expected functions")
    void testBuiltinRegistry() {
        assertTrue(BuiltinRegistry.isBuiltin("print"));
        assertTrue(BuiltinRegistry.isBuiltin("str"));
        assertTrue(BuiltinRegistry.isBuiltin("int"));
        assertTrue(BuiltinRegistry.isBuiltin("float"));
        assertTrue(BuiltinRegistry.isBuiltin("bool"));
        assertTrue(BuiltinRegistry.isBuiltin("len"));
        assertTrue(BuiltinRegistry.isBuiltin("abs"));
        assertTrue(BuiltinRegistry.isBuiltin("min"));
        assertTrue(BuiltinRegistry.isBuiltin("max"));
        assertTrue(BuiltinRegistry.isBuiltin("range"));
        
        assertFalse(BuiltinRegistry.isBuiltin("nonexistent"));
        
        // Check that we have a reasonable number of builtins
        assertTrue(BuiltinRegistry.getBuiltinCount() >= 10);
    }
    
    @Test
    @DisplayName("Test int() builtin function")
    void testIntFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test int() with no arguments
        String result = executor.executeCode("print(int())");
        assertTrue(result.contains("0"));
        
        // Test int() with string
        result = executor.executeCode("print(int('42'))");
        assertTrue(result.contains("42"));
        
        // Test int() with float
        result = executor.executeCode("print(int(3.14))");
        assertTrue(result.contains("3"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test float() builtin function")
    void testFloatFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test float() with no arguments
        String result = executor.executeCode("print(float())");
        assertTrue(result.contains("0.0"));
        
        // Test float() with string
        result = executor.executeCode("print(float('3.14'))");
        assertTrue(result.contains("3.14"));
        
        // Test float() with int
        result = executor.executeCode("print(float(42))");
        assertTrue(result.contains("42.0"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test bool() builtin function")
    void testBoolFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test bool() with no arguments
        String result = executor.executeCode("print(bool())");
        assertTrue(result.contains("False"));
        
        // Test bool() with truthy values
        result = executor.executeCode("print(bool(1))");
        assertTrue(result.contains("True"));
        
        result = executor.executeCode("print(bool('hello'))");
        assertTrue(result.contains("True"));
        
        // Test bool() with falsy values
        result = executor.executeCode("print(bool(0))");
        assertTrue(result.contains("False"));
        
        result = executor.executeCode("print(bool(''))");
        assertTrue(result.contains("False"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test len() builtin function")
    void testLenFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test len() with string
        String result = executor.executeCode("print(len('hello'))");
        assertTrue(result.contains("5"));
        
        // Test len() with list
        result = executor.executeCode("print(len([1, 2, 3, 4]))");
        assertTrue(result.contains("4"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test abs() builtin function")
    void testAbsFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test abs() with positive number
        String result = executor.executeCode("print(abs(5))");
        assertTrue(result.contains("5"));
        
        // Test abs() with negative number
        result = executor.executeCode("print(abs(-5))");
        assertTrue(result.contains("5"));
        
        // Test abs() with float
        result = executor.executeCode("print(abs(-3.14))");
        assertTrue(result.contains("3.14"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test min() and max() builtin functions")
    void testMinMaxFunctions() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test min() with multiple arguments
        String result = executor.executeCode("print(min(5, 2, 8, 1))");
        assertTrue(result.contains("1"));
        
        // Test max() with multiple arguments
        result = executor.executeCode("print(max(5, 2, 8, 1))");
        assertTrue(result.contains("8"));
        
        // Test min() with list
        result = executor.executeCode("print(min([5, 2, 8, 1]))");
        assertTrue(result.contains("1"));
        
        // Test max() with list
        result = executor.executeCode("print(max([5, 2, 8, 1]))");
        assertTrue(result.contains("8"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test range() builtin function")
    void testRangeFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test range() with single argument
        String result = executor.executeCode("print(list(range(5)))");
        assertTrue(result.contains("[0, 1, 2, 3, 4]"));
        
        // Test range() with start and stop
        result = executor.executeCode("print(list(range(2, 6)))");
        assertTrue(result.contains("[2, 3, 4, 5]"));
        
        // Test range() with start, stop, and step
        result = executor.executeCode("print(list(range(0, 10, 2)))");
        assertTrue(result.contains("[0, 2, 4, 6, 8]"));
        
        executor.close();
    }
}
