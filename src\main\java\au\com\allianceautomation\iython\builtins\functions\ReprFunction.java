package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.Map;

/**
 * Python repr() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ReprFunction extends AbstractBuiltinFunction {
    
    public ReprFunction() {
        super("repr", 1, 1, "repr(object) -> string");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (obj == null) {
            return "None";
        } else if (obj instanceof String) {
            // For strings, add quotes and escape special characters
            String str = (String) obj;
            StringBuilder sb = new StringBuilder("'");
            for (char c : str.toCharArray()) {
                switch (c) {
                    case '\'':
                        sb.append("\\'");
                        break;
                    case '\\':
                        sb.append("\\\\");
                        break;
                    case '\n':
                        sb.append("\\n");
                        break;
                    case '\r':
                        sb.append("\\r");
                        break;
                    case '\t':
                        sb.append("\\t");
                        break;
                    default:
                        if (c < 32 || c > 126) {
                            sb.append(String.format("\\x%02x", (int) c));
                        } else {
                            sb.append(c);
                        }
                        break;
                }
            }
            sb.append("'");
            return sb.toString();
        } else if (obj instanceof Boolean) {
            return ((Boolean) obj) ? "True" : "False";
        } else if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            StringBuilder sb = new StringBuilder("[");
            for (int i = 0; i < list.size(); i++) {
                if (i > 0) sb.append(", ");
                // Recursively call repr on each element
                sb.append(execute(List.of(list.get(i))));
            }
            sb.append("]");
            return sb.toString();
        } else if (obj instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) obj;
            StringBuilder sb = new StringBuilder("{");
            boolean first = true;
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                if (!first) sb.append(", ");
                first = false;
                sb.append(execute(List.of(entry.getKey())));
                sb.append(": ");
                sb.append(execute(List.of(entry.getValue())));
            }
            sb.append("}");
            return sb.toString();
        } else {
            // For other types, use pythonStr
            return pythonStr(obj);
        }
    }
}
