package au.com.allianceautomation.iython.builtins;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import au.com.allianceautomation.iython.builtins.functions.AbsFunction;
import au.com.allianceautomation.iython.builtins.functions.BinFunction;
import au.com.allianceautomation.iython.builtins.functions.BoolFunction;
import au.com.allianceautomation.iython.builtins.functions.ChrFunction;
import au.com.allianceautomation.iython.builtins.functions.DictFunction;
import au.com.allianceautomation.iython.builtins.functions.DirFunction;
import au.com.allianceautomation.iython.builtins.functions.DivModFunction;
import au.com.allianceautomation.iython.builtins.functions.EnumerateFunction;
import au.com.allianceautomation.iython.builtins.functions.FloatFunction;
import au.com.allianceautomation.iython.builtins.functions.GetAttrFunction;
import au.com.allianceautomation.iython.builtins.functions.HasAttrFunction;
import au.com.allianceautomation.iython.builtins.functions.HexFunction;
import au.com.allianceautomation.iython.builtins.functions.IntFunction;
import au.com.allianceautomation.iython.builtins.functions.IsInstanceFunction;
import au.com.allianceautomation.iython.builtins.functions.LenFunction;
import au.com.allianceautomation.iython.builtins.functions.ListFunction;
import au.com.allianceautomation.iython.builtins.functions.MaxFunction;
import au.com.allianceautomation.iython.builtins.functions.MinFunction;
import au.com.allianceautomation.iython.builtins.functions.OctFunction;
import au.com.allianceautomation.iython.builtins.functions.OrdFunction;
import au.com.allianceautomation.iython.builtins.functions.PowFunction;
import au.com.allianceautomation.iython.builtins.functions.PrintFunction;
import au.com.allianceautomation.iython.builtins.functions.RangeFunction;
import au.com.allianceautomation.iython.builtins.functions.ReprFunction;
import au.com.allianceautomation.iython.builtins.functions.ReversedFunction;
import au.com.allianceautomation.iython.builtins.functions.RoundFunction;
import au.com.allianceautomation.iython.builtins.functions.SetAttrFunction;
import au.com.allianceautomation.iython.builtins.functions.SetFunction;
import au.com.allianceautomation.iython.builtins.functions.SortedFunction;
import au.com.allianceautomation.iython.builtins.functions.StrFunction;
import au.com.allianceautomation.iython.builtins.functions.SumFunction;
import au.com.allianceautomation.iython.builtins.functions.TupleFunction;
import au.com.allianceautomation.iython.builtins.functions.TypeFunction;
import au.com.allianceautomation.iython.builtins.functions.ZipFunction;

/**
 * Registry for all Python builtin functions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class BuiltinRegistry {
    
    private static final Map<String, BuiltinFunction> builtins = new HashMap<>();
    
    static {
        // Initialize all builtin functions

        // Type conversion functions
        registerBuiltin(new IntFunction());
        registerBuiltin(new FloatFunction());
        registerBuiltin(new BoolFunction());
        registerBuiltin(new StrFunction());
        registerBuiltin(new ListFunction());
        registerBuiltin(new DictFunction());
        registerBuiltin(new TupleFunction());
        registerBuiltin(new SetFunction());

        // I/O functions
        registerBuiltin(new PrintFunction());

        // Math functions
        registerBuiltin(new AbsFunction());
        registerBuiltin(new MinFunction());
        registerBuiltin(new MaxFunction());
        registerBuiltin(new SumFunction());
        registerBuiltin(new RoundFunction());
        registerBuiltin(new PowFunction());
        registerBuiltin(new DivModFunction());

        // Sequence functions
        registerBuiltin(new LenFunction());
        registerBuiltin(new RangeFunction());
        registerBuiltin(new EnumerateFunction());
        registerBuiltin(new ZipFunction());
        registerBuiltin(new SortedFunction());
        registerBuiltin(new ReversedFunction());

        // String/Character functions
        registerBuiltin(new OrdFunction());
        registerBuiltin(new ChrFunction());
        registerBuiltin(new ReprFunction());

        // Number base conversion functions
        registerBuiltin(new HexFunction());
        registerBuiltin(new OctFunction());
        registerBuiltin(new BinFunction());

        // Object introspection functions
        registerBuiltin(new TypeFunction());
        registerBuiltin(new IsInstanceFunction());
        registerBuiltin(new HasAttrFunction());
        registerBuiltin(new GetAttrFunction());
        registerBuiltin(new SetAttrFunction());
        registerBuiltin(new DirFunction());
    }
    
    /**
     * Register a builtin function.
     * 
     * @param function The function to register
     */
    private static void registerBuiltin(BuiltinFunction function) {
        builtins.put(function.getName(), function);
    }
    
    /**
     * Get a builtin function by name.
     * 
     * @param name The name of the function
     * @return The builtin function, or null if not found
     */
    public static BuiltinFunction getBuiltin(String name) {
        return builtins.get(name);
    }
    
    /**
     * Check if a name is a builtin function.
     * 
     * @param name The name to check
     * @return true if it's a builtin function
     */
    public static boolean isBuiltin(String name) {
        return builtins.containsKey(name);
    }
    
    /**
     * Get all builtin function names.
     * 
     * @return A set of all builtin function names
     */
    public static Set<String> getBuiltinNames() {
        return builtins.keySet();
    }
    
    /**
     * Get the total number of registered builtin functions.
     * 
     * @return The number of builtin functions
     */
    public static int getBuiltinCount() {
        return builtins.size();
    }
}
