package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python int() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class IntFunction extends AbstractBuiltinFunction {
    
    public IntFunction() {
        super("int", 0, 2, "int([x]) -> integer or int(x, base=10) -> integer");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            return 0;
        }
        
        Object value = args.get(0);
        int base = 10;
        
        if (args.size() > 1) {
            Object baseObj = args.get(1);
            if (baseObj instanceof Number) {
                base = ((Number) baseObj).intValue();
            } else {
                throw new RuntimeException("int() base must be an integer");
            }
        }
        
        if (value instanceof Number) {
            return ((Number) value).intValue();
        } else if (value instanceof String) {
            String str = (String) value;
            try {
                if (base == 10) {
                    return Integer.parseInt(str.trim());
                } else {
                    return Integer.parseInt(str.trim(), base);
                }
            } catch (NumberFormatException e) {
                throw new RuntimeException("invalid literal for int() with base " + base + ": '" + str + "'");
            }
        } else if (value instanceof Boolean) {
            return ((Boolean) value) ? 1 : 0;
        } else {
            throw new RuntimeException("int() argument must be a string, a bytes-like object or a number, not '" + 
                                     value.getClass().getSimpleName() + "'");
        }
    }
}
