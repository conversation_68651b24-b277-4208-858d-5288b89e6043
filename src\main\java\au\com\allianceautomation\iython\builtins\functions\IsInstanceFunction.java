package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Python isinstance() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class IsInstanceFunction extends AbstractBuiltinFunction {
    
    public IsInstanceFunction() {
        super("isinstance", 2, 2, "isinstance(obj, class_or_tuple) -> bool");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        Object classInfo = args.get(1);
        
        if (!(classInfo instanceof String)) {
            throw new RuntimeException("isinstance() arg 2 must be a type or tuple of types");
        }
        
        String typeName = (String) classInfo;
        
        // Handle built-in type checking
        switch (typeName) {
            case "int":
                return obj instanceof Integer || obj instanceof Long;
            case "float":
                return obj instanceof Float || obj instanceof Double;
            case "bool":
                return obj instanceof Boolean;
            case "str":
                return obj instanceof String;
            case "list":
                return obj instanceof List;
            case "dict":
                return obj instanceof Map;
            case "set":
                return obj instanceof Set;
            case "tuple":
                return obj instanceof List && ((List<?>) obj).getClass().getName().contains("Unmodifiable");
            case "NoneType":
                return obj == null;
            default:
                // For other types, do a simple class name comparison
                if (obj == null) return false;
                String objTypeName = obj.getClass().getSimpleName().toLowerCase();
                return objTypeName.equals(typeName.toLowerCase());
        }
    }
}
