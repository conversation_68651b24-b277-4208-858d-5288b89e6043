package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.lang.reflect.Field;
import java.util.List;

/**
 * Python setattr() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class SetAttrFunction extends AbstractBuiltinFunction {
    
    public SetAttrFunction() {
        super("setattr", 3, 3, "setattr(obj, name, value)");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        Object name = args.get(1);
        Object value = args.get(2);
        
        if (!(name instanceof String)) {
            throw new RuntimeException("setattr expected str object, not '" + name.getClass().getSimpleName() + "'");
        }
        
        if (obj == null) {
            throw new RuntimeException("'NoneType' object has no attribute '" + name + "'");
        }
        
        String attrName = (String) name;
        Class<?> objClass = obj.getClass();
        
        // Try to set field value
        try {
            Field field = objClass.getField(attrName);
            field.set(obj, value);
            return null; // setattr returns None
        } catch (NoSuchFieldException e) {
            throw new RuntimeException("'" + objClass.getSimpleName() + "' object has no attribute '" + attrName + "'");
        } catch (IllegalAccessException e) {
            throw new RuntimeException("can't set attribute '" + attrName + "'");
        }
    }
}
