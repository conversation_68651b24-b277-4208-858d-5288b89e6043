package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Python zip() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ZipFunction extends AbstractBuiltinFunction {
    
    public ZipFunction() {
        super("zip", 0, -1, "zip(iter1 [,iter2 [...]]) -> iterator");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            return new ArrayList<>();
        }
        
        // Convert all arguments to lists
        List<List<Object>> iterables = new ArrayList<>();
        int minLength = Integer.MAX_VALUE;
        
        for (Object arg : args) {
            List<Object> items = new ArrayList<>();
            
            if (arg instanceof List) {
                List<?> list = (List<?>) arg;
                for (Object item : list) {
                    items.add(item);
                }
            } else if (arg instanceof String) {
                String str = (String) arg;
                for (int i = 0; i < str.length(); i++) {
                    items.add(String.valueOf(str.charAt(i)));
                }
            } else {
                throw new RuntimeException("'" + arg.getClass().getSimpleName() + "' object is not iterable");
            }
            
            iterables.add(items);
            minLength = Math.min(minLength, items.size());
        }
        
        // Create tuples by zipping the iterables
        List<Object> result = new ArrayList<>();
        for (int i = 0; i < minLength; i++) {
            List<Object> tuple = new ArrayList<>();
            for (List<Object> iterable : iterables) {
                tuple.add(iterable.get(i));
            }
            result.add(Collections.unmodifiableList(tuple));
        }
        
        return result;
    }
}
