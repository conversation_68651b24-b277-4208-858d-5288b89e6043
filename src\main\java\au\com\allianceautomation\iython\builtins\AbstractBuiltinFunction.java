package au.com.allianceautomation.iython.builtins;

import java.util.List;

/**
 * Abstract base class for Python builtin functions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public abstract class AbstractBuiltinFunction implements BuiltinFunction {
    
    private final String name;
    private final int minArgs;
    private final int maxArgs;
    private final String description;
    
    /**
     * Constructor for builtin function.
     * 
     * @param name The function name
     * @param minArgs Minimum number of arguments
     * @param maxArgs Maximum number of arguments (-1 for unlimited)
     * @param description Function description
     */
    protected AbstractBuiltinFunction(String name, int minArgs, int maxArgs, String description) {
        this.name = name;
        this.minArgs = minArgs;
        this.maxArgs = maxArgs;
        this.description = description;
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public int getMinArgs() {
        return minArgs;
    }
    
    @Override
    public int getMaxArgs() {
        return maxArgs;
    }
    
    @Override
    public String getDescription() {
        return description;
    }
    
    @Override
    public final Object call(List<Object> args) {
        // Validate argument count
        if (args.size() < minArgs) {
            throw new RuntimeException(name + "() missing required positional arguments");
        }
        if (maxArgs != -1 && args.size() > maxArgs) {
            throw new RuntimeException(name + "() takes at most " + maxArgs + " arguments (" + args.size() + " given)");
        }
        
        return execute(args);
    }
    
    /**
     * Execute the builtin function with validated arguments.
     * 
     * @param args The arguments to the function
     * @return The result of the function
     */
    protected abstract Object execute(List<Object> args);
    
    /**
     * Convert a Python object to a string representation.
     * 
     * @param obj The object to convert
     * @return String representation
     */
    protected String pythonStr(Object obj) {
        if (obj == null) {
            return "None";
        } else if (obj instanceof String) {
            return (String) obj;
        } else if (obj instanceof Boolean) {
            return ((Boolean) obj) ? "True" : "False";
        } else if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            StringBuilder sb = new StringBuilder("[");
            for (int i = 0; i < list.size(); i++) {
                if (i > 0) sb.append(", ");
                Object item = list.get(i);
                if (item instanceof String) {
                    sb.append("'").append(item).append("'");
                } else {
                    sb.append(pythonStr(item));
                }
            }
            sb.append("]");
            return sb.toString();
        } else {
            return obj.toString();
        }
    }
    
    /**
     * Check if an object is truthy in Python.
     * 
     * @param obj The object to check
     * @return true if truthy, false if falsy
     */
    protected boolean isTruthy(Object obj) {
        if (obj == null) return false;
        if (obj instanceof Boolean) return (Boolean) obj;
        if (obj instanceof Number) {
            if (obj instanceof Integer) return (Integer) obj != 0;
            if (obj instanceof Long) return (Long) obj != 0L;
            if (obj instanceof Double) return (Double) obj != 0.0;
            if (obj instanceof Float) return (Float) obj != 0.0f;
        }
        if (obj instanceof String) return !((String) obj).isEmpty();
        if (obj instanceof List) return !((List<?>) obj).isEmpty();
        return true; // Default to truthy for unknown types
    }
    
    @Override
    public String toString() {
        return "<built-in function " + name + ">";
    }
}
