package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python float() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class FloatFunction extends AbstractBuiltinFunction {
    
    public FloatFunction() {
        super("float", 0, 1, "float([x]) -> floating point number");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            return 0.0;
        }
        
        Object value = args.get(0);
        
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            String str = ((String) value).trim();
            try {
                if ("inf".equalsIgnoreCase(str) || "infinity".equalsIgnoreCase(str)) {
                    return Double.POSITIVE_INFINITY;
                } else if ("-inf".equalsIgnoreCase(str) || "-infinity".equalsIgnoreCase(str)) {
                    return Double.NEGATIVE_INFINITY;
                } else if ("nan".equalsIgnoreCase(str)) {
                    return Double.NaN;
                } else {
                    return Double.parseDouble(str);
                }
            } catch (NumberFormatException e) {
                throw new RuntimeException("could not convert string to float: '" + str + "'");
            }
        } else if (value instanceof Boolean) {
            return ((Boolean) value) ? 1.0 : 0.0;
        } else {
            throw new RuntimeException("float() argument must be a string or a number, not '" + 
                                     value.getClass().getSimpleName() + "'");
        }
    }
}
