package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python oct() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class OctFunction extends AbstractBuiltinFunction {
    
    public OctFunction() {
        super("oct", 1, 1, "oct(number) -> string");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (!(obj instanceof Number)) {
            throw new RuntimeException("'" + obj.getClass().getSimpleName() + "' object cannot be interpreted as an integer");
        }
        
        long value = ((Number) obj).longValue();
        
        if (value >= 0) {
            return "0o" + Long.toOctalString(value);
        } else {
            return "-0o" + Long.toOctalString(-value);
        }
    }
}
