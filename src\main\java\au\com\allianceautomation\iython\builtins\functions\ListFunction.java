package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.ArrayList;
import java.util.List;

/**
 * Python list() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ListFunction extends AbstractBuiltinFunction {
    
    public ListFunction() {
        super("list", 0, 1, "list([iterable]) -> list");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            return new ArrayList<>();
        }
        
        Object iterable = args.get(0);
        
        if (iterable instanceof List) {
            return new ArrayList<>((List<?>) iterable);
        } else if (iterable instanceof String) {
            String str = (String) iterable;
            List<Object> result = new ArrayList<>();
            for (int i = 0; i < str.length(); i++) {
                result.add(String.valueOf(str.charAt(i)));
            }
            return result;
        } else if (iterable.getClass().isArray()) {
            List<Object> result = new ArrayList<>();
            int length = java.lang.reflect.Array.getLength(iterable);
            for (int i = 0; i < length; i++) {
                result.add(java.lang.reflect.Array.get(iterable, i));
            }
            return result;
        } else {
            throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
        }
    }
}
