package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Python sorted() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class SortedFunction extends AbstractBuiltinFunction {
    
    public SortedFunction() {
        super("sorted", 1, 1, "sorted(iterable) -> new sorted list");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object iterable = args.get(0);
        
        List<Object> items = new ArrayList<>();
        
        if (iterable instanceof List) {
            List<?> list = (List<?>) iterable;
            for (Object item : list) {
                items.add(item);
            }
        } else if (iterable instanceof String) {
            String str = (String) iterable;
            for (int i = 0; i < str.length(); i++) {
                items.add(String.valueOf(str.charAt(i)));
            }
        } else {
            throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
        }
        
        // Sort the items
        try {
            Collections.sort(items, (a, b) -> {
                if (a instanceof Comparable && b instanceof Comparable) {
                    @SuppressWarnings("unchecked")
                    Comparable<Object> ca = (Comparable<Object>) a;
                    return ca.compareTo(b);
                } else {
                    throw new RuntimeException("'<' not supported between instances of '" + 
                                             a.getClass().getSimpleName() + "' and '" + 
                                             b.getClass().getSimpleName() + "'");
                }
            });
        } catch (ClassCastException e) {
            throw new RuntimeException("'<' not supported between instances of different types");
        }
        
        return items;
    }
}
