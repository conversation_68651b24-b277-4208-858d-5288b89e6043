package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.Map;

/**
 * Python len() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class LenFunction extends AbstractBuiltinFunction {
    
    public LenFunction() {
        super("len", 1, 1, "len(obj) -> int");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (obj instanceof String) {
            return ((String) obj).length();
        } else if (obj instanceof List) {
            return ((List<?>) obj).size();
        } else if (obj instanceof Map) {
            return ((Map<?, ?>) obj).size();
        } else if (obj.getClass().isArray()) {
            return java.lang.reflect.Array.getLength(obj);
        } else {
            throw new RuntimeException("object of type '" + obj.getClass().getSimpleName() + "' has no len()");
        }
    }
}
