package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Python type() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class TypeFunction extends AbstractBuiltinFunction {
    
    public TypeFunction() {
        super("type", 1, 1, "type(object) -> the object's type");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (obj == null) {
            return "<class 'NoneType'>";
        } else if (obj instanceof Boolean) {
            return "<class 'bool'>";
        } else if (obj instanceof Integer || obj instanceof Long) {
            return "<class 'int'>";
        } else if (obj instanceof Float || obj instanceof Double) {
            return "<class 'float'>";
        } else if (obj instanceof String) {
            return "<class 'str'>";
        } else if (obj instanceof List) {
            return "<class 'list'>";
        } else if (obj instanceof Map) {
            return "<class 'dict'>";
        } else if (obj instanceof Set) {
            return "<class 'set'>";
        } else {
            // For other Java objects, return a generic type representation
            String className = obj.getClass().getSimpleName();
            return "<class '" + className.toLowerCase() + "'>";
        }
    }
}
