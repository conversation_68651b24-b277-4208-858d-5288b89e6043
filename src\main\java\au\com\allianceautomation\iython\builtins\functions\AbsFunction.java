package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python abs() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class AbsFunction extends AbstractBuiltinFunction {
    
    public AbsFunction() {
        super("abs", 1, 1, "abs(number) -> number");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object value = args.get(0);
        
        if (value instanceof Integer) {
            return Math.abs((Integer) value);
        } else if (value instanceof Long) {
            return Math.abs((Long) value);
        } else if (value instanceof Double) {
            return Math.abs((Double) value);
        } else if (value instanceof Float) {
            return Math.abs((Float) value);
        } else {
            throw new RuntimeException("bad operand type for abs(): '" + value.getClass().getSimpleName() + "'");
        }
    }
}
