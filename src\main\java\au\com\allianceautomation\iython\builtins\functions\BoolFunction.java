package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python bool() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class BoolFunction extends AbstractBuiltinFunction {
    
    public BoolFunction() {
        super("bool", 0, 1, "bool([x]) -> bool");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            return false;
        }
        
        return isTruthy(args.get(0));
    }
}
