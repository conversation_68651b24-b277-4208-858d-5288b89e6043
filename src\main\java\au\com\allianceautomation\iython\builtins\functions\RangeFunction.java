package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.ArrayList;
import java.util.List;

/**
 * Python range() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class RangeFunction extends AbstractBuiltinFunction {
    
    public RangeFunction() {
        super("range", 1, 3, "range(stop) or range(start, stop[, step])");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        int start = 0;
        int stop;
        int step = 1;
        
        if (args.size() == 1) {
            stop = ((Number) args.get(0)).intValue();
        } else if (args.size() == 2) {
            start = ((Number) args.get(0)).intValue();
            stop = ((Number) args.get(1)).intValue();
        } else {
            start = ((Number) args.get(0)).intValue();
            stop = ((Number) args.get(1)).intValue();
            step = ((Number) args.get(2)).intValue();
        }
        
        if (step == 0) {
            throw new RuntimeException("range() arg 3 must not be zero");
        }
        
        List<Object> result = new ArrayList<>();
        if (step > 0) {
            for (int i = start; i < stop; i += step) {
                result.add(i);
            }
        } else {
            for (int i = start; i > stop; i += step) {
                result.add(i);
            }
        }
        
        return result;
    }
}
