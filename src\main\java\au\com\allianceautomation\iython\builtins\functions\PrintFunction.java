package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python print() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PrintFunction extends AbstractBuiltinFunction {
    
    public PrintFunction() {
        super("print", 0, -1, "print(*values, sep=' ', end='\\n', file=sys.stdout, flush=False)");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        StringBuilder output = new StringBuilder();
        
        for (int i = 0; i < args.size(); i++) {
            if (i > 0) output.append(" ");
            output.append(pythonStr(args.get(i)));
        }
        output.append("\n");
        
        // For now, we'll return the output string
        // The PythonRuntime will handle actual printing
        return output.toString();
    }
}
