package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.HashSet;
import java.util.List;

/**
 * Python set() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class SetFunction extends AbstractBuiltinFunction {
    
    public SetFunction() {
        super("set", 0, 1, "set([iterable]) -> new set object");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        // TODO: Implement set construction
        return new HashSet<>();
    }
}
