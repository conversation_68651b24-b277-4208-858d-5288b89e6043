package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Python reversed() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ReversedFunction extends AbstractBuiltinFunction {
    
    public ReversedFunction() {
        super("reversed", 1, 1, "reversed(seq) -> iterator");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object sequence = args.get(0);
        
        List<Object> items = new ArrayList<>();
        
        if (sequence instanceof List) {
            List<?> list = (List<?>) sequence;
            for (Object item : list) {
                items.add(item);
            }
        } else if (sequence instanceof String) {
            String str = (String) sequence;
            for (int i = 0; i < str.length(); i++) {
                items.add(String.valueOf(str.charAt(i)));
            }
        } else {
            throw new RuntimeException("'" + sequence.getClass().getSimpleName() + "' object is not reversible");
        }
        
        // Reverse the items
        Collections.reverse(items);
        
        return items;
    }
}
