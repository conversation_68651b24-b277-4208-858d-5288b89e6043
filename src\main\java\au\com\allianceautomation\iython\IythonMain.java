package au.com.allianceautomation.iython;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Main entry point for the iython application.
 * This class provides the main method to run Python code within the JVM.
 *
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class IythonMain {

    private static final Logger logger = LoggerFactory.getLogger(IythonMain.class);

    public static void main(String[] args) {
        logger.info("Starting iython - Python execution in JVM");

        try {
            PythonExecutor executor = new PythonExecutor();

            if (args.length == 0) {
                // Start interactive console if no arguments provided
                startInteractiveConsole(executor);
            } else if (args.length == 1) {
                String arg = args[0];
                if ("--interactive".equals(arg) || "-i".equals(arg)) {
                    // Start interactive console
                    startInteractiveConsole(executor);
                } else if ("--demo".equals(arg) || "-d".equals(arg)) {
                    // Run demo
                    runDemo(executor);
                } else if ("--help".equals(arg) || "-h".equals(arg)) {
                    // Show help
                    showHelp();
                    return;
                } else if (arg.endsWith(".py") || arg.contains("/") || arg.contains("\\")) {
                    // Looks like a file path - execute Python file
                    logger.info("Executing Python file: {}", arg);
                    String output = executor.executeFile(arg);
                    System.out.print(output);
                } else {
                    // Execute as Python code
                    logger.info("Executing Python code: {}", arg);
                    String output = executor.executeCode(arg);
                    System.out.print(output);
                }
            } else {
                // Execute Python code directly
                String pythonCode = String.join(" ", args);
                logger.info("Executing Python code: {}", pythonCode);
                String output = executor.executeCode(pythonCode);
                System.out.print(output);
            }

            executor.close();

        } catch (Exception e) {
            logger.error("Error executing Python code", e);
            System.err.println("Error: " + e.getMessage());
            System.exit(1);
        }

        logger.info("iython execution completed");
    }

    private static void startInteractiveConsole(PythonExecutor executor) {
        logger.info("Starting interactive Python console");
        PythonConsole console = new PythonConsole(executor);
        console.start();
    }

    private static void showHelp() {
        System.out.println("iython - Python execution in JVM");
        System.out.println();
        System.out.println("Usage:");
        System.out.println("  java -jar iython.jar                    Start interactive console");
        System.out.println("  java -jar iython.jar -i, --interactive  Start interactive console");
        System.out.println("  java -jar iython.jar -d, --demo         Run demo");
        System.out.println("  java -jar iython.jar -h, --help         Show this help");
        System.out.println("  java -jar iython.jar <file.py>          Execute Python file");
        System.out.println("  java -jar iython.jar <python_code>      Execute Python code directly");
        System.out.println();
        System.out.println("Interactive console commands:");
        System.out.println("  exit() or quit()  Exit the console");
        System.out.println("  help()            Show help");
        System.out.println("  vars()            Show variables");
        System.out.println("  clear()           Clear screen");
    }
    
    private static void runDemo(PythonExecutor executor) {
        logger.info("Running iython demo");

        String demoCode = "print(\"Hello from Python running in the JVM!\")\n" +
                          "print(\"iython - Custom Python interpreter\")\n" +
                          "print(\"\")\n" +
                          "\n" +
                          "# Simple variable assignment\n" +
                          "x = 42\n" +
                          "y = \"Hello World\"\n" +
                          "print(\"x = \" + str(x))\n" +
                          "print(\"y = \" + y)\n" +
                          "\n" +
                          "# Simple arithmetic\n" +
                          "result = 10 + 5 * 2\n" +
                          "print(\"10 + 5 * 2 = \" + str(result))\n" +
                          "\n" +
                          "# List creation\n" +
                          "numbers = [1, 2, 3, 4, 5]\n" +
                          "print(\"Numbers: \" + str(numbers))";

        try {
            String output = executor.executeCode(demoCode);
            System.out.print(output);
        } catch (Exception e) {
            logger.error("Error running demo", e);
            System.err.println("Demo failed: " + e.getMessage());
        }
    }
}
