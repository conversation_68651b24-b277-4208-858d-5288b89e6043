package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Python divmod() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class DivModFunction extends AbstractBuiltinFunction {
    
    public DivModFunction() {
        super("divmod", 2, 2, "divmod(x, y) -> (div, mod)");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object x = args.get(0);
        Object y = args.get(1);
        
        if (!(x instanceof Number) || !(y instanceof Number)) {
            throw new RuntimeException("divmod() arguments must be numeric");
        }
        
        Number xNum = (Number) x;
        Number yNum = (Number) y;
        
        // Check for division by zero
        if (yNum.doubleValue() == 0.0) {
            throw new RuntimeException("integer division or modulo by zero");
        }
        
        List<Object> result = new ArrayList<>();
        
        if (x instanceof Integer && y instanceof Integer) {
            // Integer division
            int xInt = xNum.intValue();
            int yInt = yNum.intValue();
            
            int div = xInt / yInt;
            int mod = xInt % yInt;
            
            // Python's divmod behavior: adjust for negative numbers
            if ((mod != 0) && ((xInt < 0) != (yInt < 0))) {
                div -= 1;
                mod += yInt;
            }
            
            result.add(div);
            result.add(mod);
        } else {
            // Floating point division
            double xDouble = xNum.doubleValue();
            double yDouble = yNum.doubleValue();
            
            double div = Math.floor(xDouble / yDouble);
            double mod = xDouble - (div * yDouble);
            
            result.add(div);
            result.add(mod);
        }
        
        return Collections.unmodifiableList(result);
    }
}
