package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python min() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class MinFunction extends AbstractBuiltinFunction {
    
    public MinFunction() {
        super("min", 1, -1, "min(iterable, *[, default, key]) or min(arg1, arg2, *args[, key])");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.size() == 1) {
            // Single iterable argument
            Object iterable = args.get(0);
            if (iterable instanceof List) {
                List<?> list = (List<?>) iterable;
                if (list.isEmpty()) {
                    throw new RuntimeException("min() arg is an empty sequence");
                }
                Object min = list.get(0);
                for (int i = 1; i < list.size(); i++) {
                    if (compareObjects(list.get(i), min) < 0) {
                        min = list.get(i);
                    }
                }
                return min;
            } else {
                throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
            }
        } else {
            // Multiple arguments
            Object min = args.get(0);
            for (int i = 1; i < args.size(); i++) {
                if (compareObjects(args.get(i), min) < 0) {
                    min = args.get(i);
                }
            }
            return min;
        }
    }
    
    @SuppressWarnings("unchecked")
    private int compareObjects(Object a, Object b) {
        if (a instanceof Comparable && b instanceof Comparable) {
            try {
                return ((Comparable<Object>) a).compareTo(b);
            } catch (ClassCastException e) {
                throw new RuntimeException("'<' not supported between instances of '" + 
                                         a.getClass().getSimpleName() + "' and '" + 
                                         b.getClass().getSimpleName() + "'");
            }
        } else {
            throw new RuntimeException("'<' not supported between instances of '" + 
                                     a.getClass().getSimpleName() + "' and '" + 
                                     b.getClass().getSimpleName() + "'");
        }
    }
}
