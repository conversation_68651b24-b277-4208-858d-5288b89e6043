package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python max() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class MaxFunction extends AbstractBuiltinFunction {
    
    public MaxFunction() {
        super("max", 1, -1, "max(iterable, *[, default, key]) or max(arg1, arg2, *args[, key])");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.size() == 1) {
            // Single iterable argument
            Object iterable = args.get(0);
            if (iterable instanceof List) {
                List<?> list = (List<?>) iterable;
                if (list.isEmpty()) {
                    throw new RuntimeException("max() arg is an empty sequence");
                }
                Object max = list.get(0);
                for (int i = 1; i < list.size(); i++) {
                    if (compareObjects(list.get(i), max) > 0) {
                        max = list.get(i);
                    }
                }
                return max;
            } else {
                throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
            }
        } else {
            // Multiple arguments
            Object max = args.get(0);
            for (int i = 1; i < args.size(); i++) {
                if (compareObjects(args.get(i), max) > 0) {
                    max = args.get(i);
                }
            }
            return max;
        }
    }
    
    @SuppressWarnings("unchecked")
    private int compareObjects(Object a, Object b) {
        if (a instanceof Comparable && b instanceof Comparable) {
            try {
                return ((Comparable<Object>) a).compareTo(b);
            } catch (ClassCastException e) {
                throw new RuntimeException("'>' not supported between instances of '" + 
                                         a.getClass().getSimpleName() + "' and '" + 
                                         b.getClass().getSimpleName() + "'");
            }
        } else {
            throw new RuntimeException("'>' not supported between instances of '" + 
                                     a.getClass().getSimpleName() + "' and '" + 
                                     b.getClass().getSimpleName() + "'");
        }
    }
}
