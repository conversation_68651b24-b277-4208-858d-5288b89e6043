package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python ord() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class OrdFunction extends AbstractBuiltinFunction {
    
    public OrdFunction() {
        super("ord", 1, 1, "ord(c) -> integer");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (!(obj instanceof String)) {
            throw new RuntimeException("ord() expected str object, not '" + obj.getClass().getSimpleName() + "'");
        }
        
        String str = (String) obj;
        
        if (str.length() != 1) {
            throw new RuntimeException("ord() expected a character, but string of length " + str.length() + " found");
        }
        
        return (int) str.charAt(0);
    }
}
