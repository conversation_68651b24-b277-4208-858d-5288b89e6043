package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

/**
 * Python hasattr() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class HasAttrFunction extends AbstractBuiltinFunction {
    
    public HasAttrFunction() {
        super("hasattr", 2, 2, "hasattr(obj, name) -> bool");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        Object name = args.get(1);
        
        if (!(name instanceof String)) {
            throw new RuntimeException("hasattr expected str object, not '" + name.getClass().getSimpleName() + "'");
        }
        
        if (obj == null) {
            return false;
        }
        
        String attrName = (String) name;
        Class<?> objClass = obj.getClass();
        
        // Check for fields
        try {
            Field field = objClass.getField(attrName);
            return true;
        } catch (NoSuchFieldException e) {
            // Field not found, continue to check methods
        }
        
        // Check for methods
        try {
            Method method = objClass.getMethod(attrName);
            return true;
        } catch (NoSuchMethodException e) {
            // Method not found
        }
        
        // For built-in Python types, check common attributes
        if (obj instanceof String) {
            return hasStringAttribute(attrName);
        } else if (obj instanceof List) {
            return hasListAttribute(attrName);
        }
        
        return false;
    }
    
    private boolean hasStringAttribute(String attrName) {
        switch (attrName) {
            case "upper":
            case "lower":
            case "strip":
            case "split":
            case "replace":
            case "startswith":
            case "endswith":
            case "find":
            case "count":
                return true;
            default:
                return false;
        }
    }
    
    private boolean hasListAttribute(String attrName) {
        switch (attrName) {
            case "append":
            case "extend":
            case "insert":
            case "remove":
            case "pop":
            case "clear":
            case "index":
            case "count":
            case "sort":
            case "reverse":
                return true;
            default:
                return false;
        }
    }
}
