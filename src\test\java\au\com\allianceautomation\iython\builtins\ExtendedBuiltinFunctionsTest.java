package au.com.allianceautomation.iython.builtins;

import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import au.com.allianceautomation.iython.PythonExecutionException;
import au.com.allianceautomation.iython.PythonExecutor;

/**
 * Extended unit tests for Python builtin functions.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class ExtendedBuiltinFunctionsTest {
    
    @Test
    @DisplayName("Test builtin registry has all expected functions")
    void testExtendedBuiltinRegistry() {
        // Math functions
        assertTrue(BuiltinRegistry.isBuiltin("round"));
        assertTrue(BuiltinRegistry.isBuiltin("pow"));
        assertTrue(BuiltinRegistry.isBuiltin("divmod"));
        
        // String/Character functions
        assertTrue(BuiltinRegistry.isBuiltin("ord"));
        assertTrue(BuiltinRegistry.isBuiltin("chr"));
        assertTrue(BuiltinRegistry.isBuiltin("repr"));
        
        // Number base conversion
        assertTrue(BuiltinRegistry.isBuiltin("hex"));
        assertTrue(BuiltinRegistry.isBuiltin("oct"));
        assertTrue(BuiltinRegistry.isBuiltin("bin"));
        
        // Sequence functions
        assertTrue(BuiltinRegistry.isBuiltin("enumerate"));
        assertTrue(BuiltinRegistry.isBuiltin("zip"));
        assertTrue(BuiltinRegistry.isBuiltin("sorted"));
        assertTrue(BuiltinRegistry.isBuiltin("reversed"));
        
        // Object introspection
        assertTrue(BuiltinRegistry.isBuiltin("type"));
        assertTrue(BuiltinRegistry.isBuiltin("isinstance"));
        assertTrue(BuiltinRegistry.isBuiltin("hasattr"));
        assertTrue(BuiltinRegistry.isBuiltin("getattr"));
        assertTrue(BuiltinRegistry.isBuiltin("setattr"));
        assertTrue(BuiltinRegistry.isBuiltin("dir"));
        
        // Check total count - should be significantly more than before
        assertTrue(BuiltinRegistry.getBuiltinCount() >= 30);
    }
    
    @Test
    @DisplayName("Test sum() builtin function")
    void testSumFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test sum() with list
        String result = executor.executeCode("print(sum([1, 2, 3, 4]))");
        assertTrue(result.contains("10"));
        
        // Test sum() with start value
        result = executor.executeCode("print(sum([1, 2, 3], 10))");
        assertTrue(result.contains("16"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test round() builtin function")
    void testRoundFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test round() with integer result
        String result = executor.executeCode("print(round(3.7))");
        assertTrue(result.contains("4"));
        
        // Test round() with decimal places
        result = executor.executeCode("print(round(3.14159, 2))");
        assertTrue(result.contains("3.14"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test pow() builtin function")
    void testPowFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test pow() with two arguments
        String result = executor.executeCode("print(pow(2, 3))");
        assertTrue(result.contains("8"));
        
        // Test pow() with three arguments (modular exponentiation)
        result = executor.executeCode("print(pow(2, 3, 5))");
        assertTrue(result.contains("3")); // (2^3) % 5 = 8 % 5 = 3
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test ord() and chr() builtin functions")
    void testOrdChrFunctions() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test ord()
        String result = executor.executeCode("print(ord('A'))");
        assertTrue(result.contains("65"));
        
        // Test chr()
        result = executor.executeCode("print(chr(65))");
        assertTrue(result.contains("A"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test hex(), oct(), bin() builtin functions")
    void testBaseConversionFunctions() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test hex()
        String result = executor.executeCode("print(hex(255))");
        assertTrue(result.contains("0xff"));
        
        // Test oct()
        result = executor.executeCode("print(oct(8))");
        assertTrue(result.contains("0o10"));
        
        // Test bin()
        result = executor.executeCode("print(bin(5))");
        assertTrue(result.contains("0b101"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test type() builtin function")
    void testTypeFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test type() with different objects
        String result = executor.executeCode("print(type(42))");
        assertTrue(result.contains("int"));
        
        result = executor.executeCode("print(type('hello'))");
        assertTrue(result.contains("str"));
        
        result = executor.executeCode("print(type([1, 2, 3]))");
        assertTrue(result.contains("list"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test enumerate() builtin function")
    void testEnumerateFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test enumerate() with list
        String result = executor.executeCode("print(list(enumerate(['a', 'b', 'c'])))");
        assertTrue(result.contains("0") && result.contains("a"));
        assertTrue(result.contains("1") && result.contains("b"));
        assertTrue(result.contains("2") && result.contains("c"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test zip() builtin function")
    void testZipFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test zip() with two lists
        String result = executor.executeCode("print(list(zip([1, 2, 3], ['a', 'b', 'c'])))");
        assertTrue(result.contains("1") && result.contains("a"));
        assertTrue(result.contains("2") && result.contains("b"));
        assertTrue(result.contains("3") && result.contains("c"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test sorted() and reversed() builtin functions")
    void testSortedReversedFunctions() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test sorted()
        String result = executor.executeCode("print(sorted([3, 1, 4, 1, 5]))");
        assertTrue(result.contains("[1, 1, 3, 4, 5]"));
        
        // Test reversed()
        result = executor.executeCode("print(list(reversed([1, 2, 3])))");
        assertTrue(result.contains("[3, 2, 1]"));
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test divmod() builtin function")
    void testDivModFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();
        
        // Test divmod()
        String result = executor.executeCode("print(divmod(10, 3))");
        assertTrue(result.contains("3") && result.contains("1")); // (3, 1)
        
        executor.close();
    }
    
    @Test
    @DisplayName("Test dir() builtin function")
    void testDirFunction() throws PythonExecutionException {
        PythonExecutor executor = new PythonExecutor();

        // Test that dir is a builtin function
        String result = executor.executeCode("print(dir)");
        assertTrue(result.contains("built-in function dir"));

        executor.close();
    }
}
