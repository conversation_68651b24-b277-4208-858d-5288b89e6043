package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python chr() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ChrFunction extends AbstractBuiltinFunction {
    
    public ChrFunction() {
        super("chr", 1, 1, "chr(i) -> Unicode character");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        
        if (!(obj instanceof Number)) {
            throw new RuntimeException("an integer is required (got type " + obj.getClass().getSimpleName() + ")");
        }
        
        int codePoint = ((Number) obj).intValue();
        
        if (codePoint < 0 || codePoint > 0x10FFFF) {
            throw new RuntimeException("chr() arg not in range(0x110000)");
        }
        
        return String.valueOf((char) codePoint);
    }
}
