package au.com.allianceautomation.iython;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Interactive Python console for the iython application.
 * Provides a REPL (Read-Eval-Print Loop) interface for executing Python code.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonConsole {
    
    private static final Logger logger = LoggerFactory.getLogger(PythonConsole.class);
    
    private static final String PRIMARY_PROMPT = ">>> ";
    private static final String SECONDARY_PROMPT = "... ";
    private static final String VERSION_INFO = "iython 1.0.0 - Python execution in JVM";
    
    private final PythonExecutor executor;
    private final BufferedReader reader;
    private final List<String> commandHistory;
    private boolean running;
    
    public PythonConsole(PythonExecutor executor) {
        this.executor = executor;
        this.reader = new BufferedReader(new InputStreamReader(System.in));
        this.commandHistory = new ArrayList<>();
        this.running = false;
    }
    
    /**
     * Start the interactive console.
     */
    public void start() {
        logger.info("Starting Python console");
        running = true;
        
        printWelcomeMessage();
        
        while (running) {
            try {
                String input = readInput();
                if (input != null) {
                    processInput(input);
                }
            } catch (IOException e) {
                logger.error("Error reading input", e);
                System.err.println("Error reading input: " + e.getMessage());
                break;
            } catch (Exception e) {
                logger.error("Unexpected error in console", e);
                System.err.println("Unexpected error: " + e.getMessage());
            }
        }
        
        logger.info("Python console stopped");
    }
    
    /**
     * Stop the console.
     */
    public void stop() {
        running = false;
    }
    
    private void printWelcomeMessage() {
        System.out.println(VERSION_INFO);
        System.out.println("Type \"help()\" for more information, \"exit()\" or \"quit()\" to exit.");
        System.out.println();
    }
    
    private String readInput() throws IOException {
        StringBuilder input = new StringBuilder();
        String line;
        boolean multiLine = false;

        System.out.print(PRIMARY_PROMPT);

        while ((line = reader.readLine()) != null) {
            // Check for special console commands
            String trimmedLine = line.trim();

            if (trimmedLine.equals("exit()") || trimmedLine.equals("quit()")) {
                stop();
                return null;
            }

            if (trimmedLine.equals("help()")) {
                showHelp();
                return "";
            }

            if (trimmedLine.equals("vars()")) {
                showVariables();
                return "";
            }

            if (trimmedLine.equals("clear()")) {
                clearScreen();
                return "";
            }

            input.append(line).append("\n");

            // Check if we need more input (simple heuristic)
            if (needsMoreInput(line)) {
                multiLine = true;
                System.out.print(SECONDARY_PROMPT);
            } else if (multiLine && trimmedLine.isEmpty()) {
                // Empty line ends multi-line input
                break;
            } else if (!multiLine) {
                // Single line complete
                break;
            } else {
                // Continue multi-line input
                System.out.print(SECONDARY_PROMPT);
            }
        }

        // If we reach here and line is null, it means EOF was reached
        if (line == null) {
            stop();
            return null;
        }

        String result = input.toString().trim();
        if (!result.isEmpty()) {
            commandHistory.add(result);
        }

        return result.isEmpty() ? "" : result;
    }
    
    private boolean needsMoreInput(String line) {
        String trimmed = line.trim();
        return trimmed.endsWith(":") || 
               trimmed.startsWith("def ") || 
               trimmed.startsWith("class ") ||
               trimmed.startsWith("if ") ||
               trimmed.startsWith("elif ") ||
               trimmed.startsWith("else:") ||
               trimmed.startsWith("for ") ||
               trimmed.startsWith("while ") ||
               trimmed.startsWith("try:") ||
               trimmed.startsWith("except") ||
               trimmed.startsWith("finally:") ||
               trimmed.startsWith("with ");
    }
    
    private void processInput(String input) {
        if (input.isEmpty()) {
            return;
        }
        
        try {
            String output = executor.executeCode(input);
            if (output != null && !output.trim().isEmpty()) {
                System.out.print(output);
                if (!output.endsWith("\n")) {
                    System.out.println();
                }
            }
        } catch (PythonExecutionException e) {
            System.err.println("Error: " + e.getMessage());
            logger.debug("Python execution error", e);
        }
    }
    
    private void showHelp() {
        System.out.println("iython Interactive Console Help");
        System.out.println("==============================");
        System.out.println();
        System.out.println("Console Commands:");
        System.out.println("  help()    - Show this help message");
        System.out.println("  exit()    - Exit the console");
        System.out.println("  quit()    - Exit the console");
        System.out.println("  vars()    - Show current variables");
        System.out.println("  clear()   - Clear the screen");
        System.out.println();
        System.out.println("Python Features:");
        System.out.println("  - Execute Python code interactively");
        System.out.println("  - Multi-line input support for functions, classes, etc.");
        System.out.println("  - Variable persistence across commands");
        System.out.println("  - Standard Python syntax and operations");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  >>> x = 42");
        System.out.println("  >>> print(x)");
        System.out.println("  42");
        System.out.println("  >>> def greet(name):");
        System.out.println("  ...     return f\"Hello, {name}!\"");
        System.out.println("  ...");
        System.out.println("  >>> greet(\"World\")");
        System.out.println("  'Hello, World!'");
        System.out.println();
    }
    
    private void showVariables() {
        try {
            // Get variables from executor - this is a simplified approach
            System.out.println("Current variables:");
            System.out.println("(Variable inspection not yet fully implemented)");
            System.out.println("Use standard Python commands like globals() or locals() to inspect variables.");
        } catch (Exception e) {
            System.err.println("Error showing variables: " + e.getMessage());
        }
    }
    
    private void clearScreen() {
        // Clear screen using ANSI escape codes
        System.out.print("\033[2J\033[H");
        System.out.flush();
        printWelcomeMessage();
    }
}
