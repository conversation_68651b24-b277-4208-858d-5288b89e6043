package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Python enumerate() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class EnumerateFunction extends AbstractBuiltinFunction {
    
    public EnumerateFunction() {
        super("enumerate", 1, 2, "enumerate(iterable[, start]) -> iterator");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object iterable = args.get(0);
        int start = 0;
        
        if (args.size() > 1) {
            Object startObj = args.get(1);
            if (startObj instanceof Number) {
                start = ((Number) startObj).intValue();
            } else {
                throw new RuntimeException("'" + startObj.getClass().getSimpleName() + "' object cannot be interpreted as an integer");
            }
        }
        
        List<Object> items = new ArrayList<>();
        
        if (iterable instanceof List) {
            List<?> list = (List<?>) iterable;
            for (int i = 0; i < list.size(); i++) {
                List<Object> pair = new ArrayList<>();
                pair.add(start + i);
                pair.add(list.get(i));
                items.add(Collections.unmodifiableList(pair));
            }
        } else if (iterable instanceof String) {
            String str = (String) iterable;
            for (int i = 0; i < str.length(); i++) {
                List<Object> pair = new ArrayList<>();
                pair.add(start + i);
                pair.add(String.valueOf(str.charAt(i)));
                items.add(Collections.unmodifiableList(pair));
            }
        } else {
            throw new RuntimeException("'" + iterable.getClass().getSimpleName() + "' object is not iterable");
        }
        
        return items;
    }
}
