package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

/**
 * Python getattr() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class GetAttrFunction extends AbstractBuiltinFunction {
    
    public GetAttrFunction() {
        super("getattr", 2, 3, "getattr(obj, name[, default]) -> value");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        Object obj = args.get(0);
        Object name = args.get(1);
        Object defaultValue = args.size() > 2 ? args.get(2) : null;
        
        if (!(name instanceof String)) {
            throw new RuntimeException("getattr expected str object, not '" + name.getClass().getSimpleName() + "'");
        }
        
        if (obj == null) {
            if (defaultValue != null) {
                return defaultValue;
            } else {
                throw new RuntimeException("'NoneType' object has no attribute '" + name + "'");
            }
        }
        
        String attrName = (String) name;
        Class<?> objClass = obj.getClass();
        
        // Try to get field value
        try {
            Field field = objClass.getField(attrName);
            return field.get(obj);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            // Field not found or not accessible, continue to check methods
        }
        
        // Try to get method (return a placeholder for now)
        try {
            Method method = objClass.getMethod(attrName);
            return "<bound method " + objClass.getSimpleName() + "." + attrName + ">";
        } catch (NoSuchMethodException e) {
            // Method not found
        }
        
        // For built-in Python types, return method placeholders
        if (obj instanceof String && hasStringAttribute(attrName)) {
            return "<built-in method " + attrName + " of str object>";
        } else if (obj instanceof List && hasListAttribute(attrName)) {
            return "<built-in method " + attrName + " of list object>";
        }
        
        // Attribute not found
        if (defaultValue != null) {
            return defaultValue;
        } else {
            throw new RuntimeException("'" + objClass.getSimpleName() + "' object has no attribute '" + attrName + "'");
        }
    }
    
    private boolean hasStringAttribute(String attrName) {
        switch (attrName) {
            case "upper":
            case "lower":
            case "strip":
            case "split":
            case "replace":
            case "startswith":
            case "endswith":
            case "find":
            case "count":
                return true;
            default:
                return false;
        }
    }
    
    private boolean hasListAttribute(String attrName) {
        switch (attrName) {
            case "append":
            case "extend":
            case "insert":
            case "remove":
            case "pop":
            case "clear":
            case "index":
            case "count":
            case "sort":
            case "reverse":
                return true;
            default:
                return false;
        }
    }
}
