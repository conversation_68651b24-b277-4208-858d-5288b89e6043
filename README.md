# iython

A Java project for running Python 3 code within the Java Virtual Machine (JVM) using Jython.

## Overview

**iython** is an open-source project developed by Alliance Automation Australia that enables seamless execution of Python code within Java applications. It leverages Jython to provide a bridge between Python and Java, allowing you to:

- Execute Python scripts from Java applications
- Pass data between Java and Python seamlessly
- Utilize Python libraries and algorithms within Java projects
- Maintain a single JVM deployment while leveraging both languages

## Features

- **Python Code Execution**: Execute Python code from strings or files
- **Variable Exchange**: Pass variables between Java and Python contexts
- **Resource Management**: Automatic cleanup and resource management
- **Logging**: Comprehensive logging with configurable levels
- **Error Handling**: Robust error handling with custom exceptions
- **Testing**: Complete unit test suite
- **Maven Integration**: Standard Maven project structure

## Requirements

- Java 11 or higher
- Maven 3.6 or higher
- Jython 2.7.3 (automatically managed by <PERSON><PERSON>)

## Quick Start

### 1. Clone and Build

```bash
git clone https://github.com/allianceautomation/iython.git
cd iython
mvn clean compile
```

### 2. Run the Demo

```bash
mvn exec:java -Dexec.mainClass="au.com.allianceautomation.iython.IythonMain"
```

### 3. Execute a Python File

```bash
mvn exec:java -Dexec.mainClass="au.com.allianceautomation.iython.IythonMain" -Dexec.args="hello_world.py"
```

### 4. Execute Python Code Directly

```bash
mvn exec:java -Dexec.mainClass="au.com.allianceautomation.iython.IythonMain" -Dexec.args="print('Hello from command line!')"
```

## Usage Examples

### Basic Python Execution

```java
import au.com.allianceautomation.iython.PythonExecutor;

PythonExecutor executor = new PythonExecutor();

// Execute Python code
String output = executor.executeCode("print('Hello from Python!')");
System.out.println(output);

// Execute Python file
String fileOutput = executor.executeFile("script.py");
System.out.println(fileOutput);

executor.close();
```

### Variable Exchange

```java
PythonExecutor executor = new PythonExecutor();

// Set Java variable in Python
executor.setVariable("java_data", Arrays.asList(1, 2, 3, 4, 5));

// Execute Python code using the variable
String code = """
    result = sum(java_data)
    print(f"Sum: {result}")
    """;
executor.executeCode(code);

// Get Python variable in Java
Object result = executor.executeAndGetVariable("result = 42", "result");
System.out.println("Result from Python: " + result);

executor.close();
```

## Project Structure

```
iython/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── au/com/allianceautomation/iython/
│   │   │       ├── IythonMain.java              # Main entry point
│   │   │       ├── PythonExecutor.java          # Core Python execution
│   │   │       └── PythonExecutionException.java # Custom exception
│   │   └── resources/
│   │       ├── hello_world.py                   # Sample Python script
│   │       ├── data_processing.py               # Data processing example
│   │       └── logback.xml                      # Logging configuration
│   └── test/
│       └── java/
│           └── au/com/allianceautomation/iython/
│               └── PythonExecutorTest.java      # Unit tests
├── pom.xml                                      # Maven configuration
└── README.md                                    # This file
```

## Building and Testing

### Compile the Project

```bash
mvn clean compile
```

### Run Tests

```bash
mvn test
```

### Create Executable JAR

```bash
mvn clean package
```

This creates an executable JAR file in the `target/` directory that includes all dependencies.

### Run the Executable JAR

The executable JAR provides multiple ways to run Python code:

#### Interactive Console (Default)
```bash
java -jar target/iython-1.0.0-SNAPSHOT.jar
# or explicitly
java -jar target/iython-1.0.0-SNAPSHOT.jar --interactive
```

#### Run Demo
```bash
java -jar target/iython-1.0.0-SNAPSHOT.jar --demo
```

#### Execute Python File
```bash
java -jar target/iython-1.0.0-SNAPSHOT.jar script.py
```

#### Execute Python Code Directly
```bash
java -jar target/iython-1.0.0-SNAPSHOT.jar "print('Hello World')"
```

#### Show Help
```bash
java -jar target/iython-1.0.0-SNAPSHOT.jar --help
```

### Interactive Console Features

The interactive console provides a Python REPL (Read-Eval-Print Loop) with the following features:

- **Interactive Python execution**: Type Python code and see results immediately
- **Multi-line input support**: Functions, classes, and other multi-line constructs
- **Variable persistence**: Variables remain available across commands
- **Built-in commands**:
  - `help()` - Show help information
  - `exit()` or `quit()` - Exit the console
  - `vars()` - Show current variables
  - `clear()` - Clear the screen

Example interactive session:
```
>>> x = 42
>>> y = "Hello World"
>>> print(f"{y}, the answer is {x}")
Hello World, the answer is 42
>>> exit()
```

## Configuration

### Logging

Logging is configured via `src/main/resources/logback.xml`. You can adjust log levels and output destinations as needed.

### Python Path

The executor automatically adds the `src/main/resources` directory to the Python path, allowing you to import Python modules placed there.

## Limitations

- **Custom Implementation**: This is a custom Python interpreter implementation, not a complete Python runtime
- **Language Features**: Currently supports basic Python syntax (assignments, arithmetic, print statements, lists)
- **Built-in Functions**: Limited set of built-in functions implemented (ongoing development)
- **Standard Library**: No standard library modules available yet
- **Advanced Features**: Complex features like list comprehensions, function definitions, and classes are under development

## Contributing

We welcome contributions! Please feel free to submit issues, feature requests, or pull requests.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, please contact Alliance Automation Australia:
- Email: <EMAIL>
- Website: https://allianceautomation.com.au

## Acknowledgments

- [ANTLR4](https://www.antlr.org/) for providing powerful parsing capabilities
- [Byte Buddy](https://bytebuddy.net/) for runtime bytecode generation
- Alliance Automation Australia team for development and maintenance
