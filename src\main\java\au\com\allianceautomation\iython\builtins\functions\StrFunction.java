package au.com.allianceautomation.iython.builtins.functions;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;
import java.util.List;

/**
 * Python str() builtin function.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class StrFunction extends AbstractBuiltinFunction {
    
    public StrFunction() {
        super("str", 0, 1, "str(object='') -> str");
    }
    
    @Override
    protected Object execute(List<Object> args) {
        if (args.isEmpty()) {
            return "";
        }
        
        return pythonStr(args.get(0));
    }
}
