FALSE=1
NONE=2
TRUE=3
AND=4
AS=5
ASSERT=6
ASYNC=7
AWAIT=8
BREAK=9
CLASS=10
CONTINUE=11
DEF=12
DEL=13
ELIF=14
ELSE=15
EXCEPT=16
FINALLY=17
FOR=18
FROM=19
GLOBAL=20
IF=21
IMPORT=22
IN=23
IS=24
LAMBDA=25
NONLOCAL=26
NOT=27
OR=28
PASS=29
RAISE=30
RETURN=31
TRY=32
WHILE=33
WITH=34
YIELD=35
DOT=36
ELLIPSIS=37
STAR=38
OPEN_PAREN=39
CLOSE_PAREN=40
COMMA=41
COLON=42
SEMI_COLON=43
POWER=44
ASSIGN=45
OPEN_BRACK=46
CLOSE_BRACK=47
OR_OP=48
XOR=49
AND_OP=50
LEFT_SHIFT=51
RIGHT_SHIFT=52
ADD=53
MINUS=54
DIV=55
MOD=56
IDIV=57
NOT_OP=58
OPEN_BRACE=59
CLOSE_BRACE=60
LESS_THAN=61
GREATER_THAN=62
EQUALS=63
GT_EQ=64
LT_EQ=65
NOT_EQ_1=66
NOT_EQ_2=67
AT=68
ARROW=69
ADD_ASSIGN=70
SUB_ASSIGN=71
MULT_ASSIGN=72
AT_ASSIGN=73
DIV_ASSIGN=74
MOD_ASSIGN=75
AND_ASSIGN=76
OR_ASSIGN=77
XOR_ASSIGN=78
LEFT_SHIFT_ASSIGN=79
RIGHT_SHIFT_ASSIGN=80
POWER_ASSIGN=81
IDIV_ASSIGN=82
WALRUS=83
STRING=84
NUMBER=85
INTEGER=86
NAME=87
NEWLINE=88
WS=89
COMMENT=90
SPACES=91
ErrorChar=92
'False'=1
'None'=2
'True'=3
'and'=4
'as'=5
'assert'=6
'async'=7
'await'=8
'break'=9
'class'=10
'continue'=11
'def'=12
'del'=13
'elif'=14
'else'=15
'except'=16
'finally'=17
'for'=18
'from'=19
'global'=20
'if'=21
'import'=22
'in'=23
'is'=24
'lambda'=25
'nonlocal'=26
'not'=27
'or'=28
'pass'=29
'raise'=30
'return'=31
'try'=32
'while'=33
'with'=34
'yield'=35
'.'=36
'...'=37
'*'=38
'('=39
')'=40
','=41
':'=42
';'=43
'**'=44
'='=45
'['=46
']'=47
'|'=48
'^'=49
'&'=50
'<<'=51
'>>'=52
'+'=53
'-'=54
'/'=55
'%'=56
'//'=57
'~'=58
'{'=59
'}'=60
'<'=61
'>'=62
'=='=63
'>='=64
'<='=65
'<>'=66
'!='=67
'@'=68
'->'=69
'+='=70
'-='=71
'*='=72
'@='=73
'/='=74
'%='=75
'&='=76
'|='=77
'^='=78
'<<='=79
'>>='=80
'**='=81
'//='=82
':='=83
